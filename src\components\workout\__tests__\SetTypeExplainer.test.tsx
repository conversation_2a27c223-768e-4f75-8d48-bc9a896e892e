import React from 'react'
import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'
import { SetTypeExplainer } from '../SetTypeExplainer'

describe('SetTypeExplainer', () => {
  it('should not render anything when isOpen is false', () => {
    const { container } = render(
      <SetTypeExplainer setType="Rest-pause" isOpen={false} />
    )
    expect(container.firstChild).toBeNull()
  })

  it('should render explanation when isOpen is true', () => {
    render(<SetTypeExplainer setType="Rest-pause" isOpen />)
    expect(screen.getByText(/Rest-pause sets/)).toBeInTheDocument()
  })

  it('should show correct explanation for Rest-pause', () => {
    render(<SetTypeExplainer setType="Rest-pause" isOpen />)
    expect(screen.getByText(/short rest periods/i)).toBeInTheDocument()
    expect(screen.getByText(/muscle fatigue/i)).toBeInTheDocument()
  })

  it('should show correct explanation for Drop set', () => {
    render(<SetTypeExplainer setType="Drop set" isOpen />)
    expect(screen.getByText(/reduce the weight/i)).toBeInTheDocument()
    expect(screen.getByText(/muscle exhaustion/i)).toBeInTheDocument()
  })

  it('should show correct explanation for Pyramid', () => {
    render(<SetTypeExplainer setType="Pyramid" isOpen />)
    expect(screen.getByText(/weight increases/i)).toBeInTheDocument()
    expect(screen.getByText(/reps decrease/i)).toBeInTheDocument()
  })

  it('should show correct explanation for Reverse pyramid', () => {
    render(<SetTypeExplainer setType="Reverse pyramid" isOpen />)
    expect(screen.getByText(/heaviest weight first/i)).toBeInTheDocument()
    expect(screen.getByText(/strength gains/i)).toBeInTheDocument()
  })

  it('should show correct explanation for Back-off', () => {
    render(<SetTypeExplainer setType="Back-off" isOpen />)
    expect(screen.getByText(/lighter weight/i)).toBeInTheDocument()
    expect(screen.getByText(/extra training volume/i)).toBeInTheDocument()
  })

  it('should show correct explanation for Normal', () => {
    render(<SetTypeExplainer setType="Normal" isOpen />)
    expect(screen.getByText(/Normal sets/)).toBeInTheDocument()
    expect(screen.getByText(/Standard straight sets/i)).toBeInTheDocument()
  })

  it('should show correct explanation for Warm Up', () => {
    render(<SetTypeExplainer setType="Warm Up" isOpen />)
    expect(screen.getByText(/Warm Up sets/)).toBeInTheDocument()
    expect(screen.getByText(/prepare your muscles/i)).toBeInTheDocument()
  })

  it('should use mobile-friendly styling', () => {
    render(<SetTypeExplainer setType="Drop set" isOpen />)
    const container = screen.getByRole('region')

    expect(container).toHaveClass('rounded-theme')
    expect(container).toHaveClass('p-4')
    expect(container).toHaveClass('mb-4')
  })

  it('should have smooth animation classes', () => {
    render(<SetTypeExplainer setType="Pyramid" isOpen />)
    const container = screen.getByRole('region')

    expect(container).toHaveClass('animate-in')
    expect(container).toHaveClass('slide-in-from-top-2')
  })

  it('should have proper ARIA attributes', () => {
    render(<SetTypeExplainer setType="Rest-pause" isOpen />)
    const container = screen.getByRole('region')

    expect(container).toHaveAttribute(
      'aria-label',
      'Rest-pause set explanation'
    )
  })
})
