import { test, expect } from '@playwright/test'
import { generateMockWorkout } from './helpers/test-workout-builder'
import { mockAuthAndStartWorkout } from './helpers/workout-api-mocks'

test.describe('Warmup Set Labels', () => {
  test('should display "Warm Up" label for warmup sets', async ({ page }) => {
    // Setup mock workout with warmup sets
    const workout = generateMockWorkout({
      exercise: {
        Id: 123,
        Label: 'Bench Press',
        IsBodyweight: false,
        RecommendationId: 456,
      },
      recommendation: {
        Series: 3,
        Reps: 10,
        Weight: { Lb: 135, Kg: 61.23 },
        WarmupsCount: 2,
        WarmUpReps1: 5,
        WarmUpReps2: 3,
        WarmUpWeightSet1: { Lb: 95, Kg: 43.09 },
        WarmUpWeightSet2: { Lb: 115, Kg: 52.16 },
        IsNormalSets: true,
      },
    })

    // Mock API and start workout
    await mockAuthAndStartWorkout(page, workout)

    // Navigate to exercise page
    await page.goto('/workout/exercise/123')

    // Wait for sets to load
    await page.waitForSelector('[data-testid="sets-grid"]')

    // Check that warmup sets have "Warm Up" badges
    const warmupBadges = page.locator('text="Warm Up"')
    await expect(warmupBadges).toHaveCount(2) // 2 warmup sets

    // Check that work sets have "Normal" badges
    const normalBadges = page.locator('text="Normal"')
    await expect(normalBadges).toHaveCount(3) // 3 work sets

    // Verify warmup sets show "W" as set number
    const setNumbers = page.locator('[data-testid="sets-grid"] >> text="W"')
    await expect(setNumbers).toHaveCount(2)

    // Click on a warmup badge to see the explainer
    await warmupBadges.first().click()

    // Verify warmup explainer appears
    await expect(page.locator('text="Warm Up sets"')).toBeVisible()
    await expect(page.locator('text=/prepare your muscles/')).toBeVisible()
  })

  test('should display warmup labels in mobile viewport', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })

    // Setup mock workout
    const workout = generateMockWorkout({
      exercise: {
        Id: 123,
        Label: 'Squat',
        IsBodyweight: false,
        RecommendationId: 456,
      },
      recommendation: {
        Series: 3,
        Reps: 8,
        Weight: { Lb: 225, Kg: 102.06 },
        WarmupsCount: 3,
        IsNormalSets: true,
      },
    })

    // Mock API and start workout
    await mockAuthAndStartWorkout(page, workout)

    // Navigate to exercise page
    await page.goto('/workout/exercise/123')

    // Wait for sets to load
    await page.waitForSelector('[data-testid="sets-grid"]')

    // Verify warmup badges are visible on mobile
    const warmupBadges = page.locator('text="Warm Up"')
    await expect(warmupBadges).toHaveCount(3)

    // Verify badges have minimum touch target size
    const firstBadge = warmupBadges.first()
    const box = await firstBadge.boundingBox()
    expect(box?.height).toBeGreaterThanOrEqual(32) // Compact variant minimum height
  })
})
