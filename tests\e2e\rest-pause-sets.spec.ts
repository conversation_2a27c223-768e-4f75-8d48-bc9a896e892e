import { test, expect } from '@playwright/test'

test.describe('Rest-Pause Sets', () => {
  test('should not complete exercise after saving first work set of rest-pause exercise', async ({
    page,
  }) => {
    // Mock API responses
    await page.route('**/api/auth/me', async (route) => {
      await route.fulfill({
        status: 200,
        json: {
          user: {
            id: 'test-user',
            email: '<EMAIL>',
            MassUnit: 'lb',
          },
          isFirstTimeUser: false,
        },
      })
    })

    // Mock workout with rest-pause exercise
    await page.route('**/api/workout/start', async (route) => {
      await route.fulfill({
        status: 200,
        json: {
          id: 'test-workout',
          startTime: new Date().toISOString(),
          exercises: [
            {
              Id: 123,
              Label: 'Bench Press',
              IsBodyweight: false,
              IsNormalSets: false,
            },
          ],
        },
      })
    })

    // Mock recommendation with rest-pause
    await page.route('**/api/exercise/123/recommendation', async (route) => {
      await route.fulfill({
        status: 200,
        json: {
          ExerciseId: 123,
          Series: 1, // 1 main work set
          Reps: 12,
          Weight: { Lb: 135, Kg: 61.2 },
          NbPauses: 3, // 3 rest-pause mini-sets
          NbRepsPauses: 8,
          RpRest: 15,
          IsNormalSets: false,
          WarmupsCount: 2,
          WarmUpReps1: 5,
          WarmUpReps2: 3,
          WarmUpWeightSet1: { Lb: 95, Kg: 43.1 },
          WarmUpWeightSet2: { Lb: 115, Kg: 52.2 },
          Increments: { Lb: 5, Kg: 2.5 },
        },
      })
    })

    // Mock save set response
    await page.route('**/api/workout/save-set', async (route) => {
      await route.fulfill({
        status: 200,
        json: { success: true },
      })
    })

    // Navigate to workout
    await page.goto('/workout')

    // Start workout
    await page.getByRole('button', { name: /start/i }).click()

    // Should navigate to exercise page
    await expect(page).toHaveURL(/\/workout\/exercise\/123/)

    // Complete warmup sets
    // eslint-disable-next-line no-await-in-loop
    for (let i = 0; i < 2; i++) {
      // eslint-disable-next-line no-await-in-loop
      await page.getByRole('button', { name: /save set/i }).click()
      // Wait for rest timer
      if (i < 1) {
        // eslint-disable-next-line no-await-in-loop
        await page.waitForURL(/\/workout\/rest-timer/)
        // eslint-disable-next-line no-await-in-loop
        await page.goto('/workout/exercise/123')
      }
    }

    // Now on first work set
    // Save the first work set
    await page.getByRole('button', { name: /save set/i }).click()

    // Should show RIR picker (not exercise complete)
    await expect(page.getByText(/reps in reserve/i)).toBeVisible()

    // Should NOT show exercise complete message
    await expect(page.getByText(/all sets done.*congrats/i)).not.toBeVisible()

    // Select RIR and continue
    await page.getByRole('button', { name: /2 RIR/i }).click()

    // Should navigate to rest timer, not next exercise
    await expect(page).toHaveURL(/\/workout\/rest-timer/)

    // Navigate back to exercise
    await page.goto('/workout/exercise/123')

    // Should still be on the same exercise, now on rest-pause mini-set
    await expect(page.getByText(/bench press/i)).toBeVisible()

    // Verify we're NOT on the finish exercise state
    await expect(
      page.getByRole('button', { name: /finish exercise/i })
    ).not.toBeVisible()
  })
})
