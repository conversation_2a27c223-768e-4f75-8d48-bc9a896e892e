import { apiClient } from '@/api/client'
import {
  WorkoutTemplateModel,
  RecommendationModel,
  WorkoutTemplateGroupModel,
} from '@/types/api'
import { getCurrentUserEmail } from '@/lib/auth-utils'
import { logger } from '@/utils/logger'
import { WarmupCalculator } from '@/utils/warmupCalculator'
import { ExerciseHelpers } from '@/utils/exerciseHelpers'
// import { getUserSettings } from '@/services/userSettings' // TODO: Enable when user preferences are available
import type {
  GetUserWorkoutProgramTimeZoneInfoResponse,
  GetRecommendationForExerciseRequest,
} from './workout-types'

// Export request/response types
export type {
  GetUserWorkoutProgramTimeZoneInfoResponse,
  GetRecommendationForExerciseRequest,
} from './workout-types'

/**
 * Get user's workout program information with timezone
 */
export async function getUserWorkoutProgramInfo(): Promise<GetUserWorkoutProgramTimeZoneInfoResponse | null> {
  try {
    // Create timezone info object matching API expectations
    const timeZoneInfo = {
      TimeZoneId: Intl.DateTimeFormat().resolvedOptions().timeZone,
      Offset: new Date().getTimezoneOffset() / -60,
      IsDaylightSaving: false, // Simple implementation
    }

    const response = await apiClient.post(
      '/api/WorkoutLog/GetUserWorkoutProgramTimeZoneInfo',
      timeZoneInfo
    )

    // Log response for debugging in development
    logger.debug('GetUserWorkoutProgramTimeZoneInfo response:', {
      statusCode: response.data?.StatusCode || response.data?.statusCode,
      hasResult: !!response.data?.Result || response.data?.hasResult,
      hasData:
        response.data?.hasData !== undefined
          ? response.data.hasData
          : !!response.data?.Data,
      dataKeys: response.data ? Object.keys(response.data) : [],
      fullResponse: response.data,
    })

    // Note: hasData: false doesn't mean the user has no program
    // It might just mean certain data isn't available in this specific response
    // The response might still contain workout ID and basic info

    // Handle the specific case where hasData: false but Result/result contains program info
    if (
      response.data &&
      response.data.hasData === false &&
      (response.data.Result || response.data.result)
    ) {
      // The Result/result might still have the workout template ID we need
      const result = response.data.Result || response.data.result
      logger.debug('GetUserWorkoutProgramTimeZoneInfo Result contents:', {
        hasGetUserProgramInfoResponseModel:
          !!result?.GetUserProgramInfoResponseModel,
        nextWorkoutTemplateId:
          result?.GetUserProgramInfoResponseModel?.NextWorkoutTemplate?.Id,
        nextWorkoutTemplateLabel:
          result?.GetUserProgramInfoResponseModel?.NextWorkoutTemplate?.Label,
        fullResult: result,
      })
      return result
    }

    // Handle both wrapped and direct responses (PascalCase and camelCase)
    if (
      response.data &&
      (response.data.StatusCode === 200 || response.data.statusCode === 200) &&
      (response.data.Result || response.data.result)
    ) {
      return response.data.Result || response.data.result
    }

    // Check for other common response formats
    if (response.data && response.data.Data) {
      return response.data.Data
    }

    // If no StatusCode, assume it's a direct response
    if (
      response.data &&
      !response.data.StatusCode &&
      !response.data.statusCode
    ) {
      return response.data
    }

    // Handle root-level data with StatusCode
    if (response.data && response.data.StatusCode === 200) {
      // Extract data excluding StatusCode
      const { StatusCode, ...actualData } = response.data
      // Only return if there's actual data beyond StatusCode
      if (Object.keys(actualData).length > 0) {
        return actualData
      }
    }

    // Log unexpected response format
    logger.warn(
      'Unexpected response format from GetUserWorkoutProgramTimeZoneInfo:',
      response.data
    )
    return null
  } catch (error) {
    logger.error('Error fetching user workout program info:', error)
    throw error
  }
}

/**
 * Get user's workout templates
 */
export async function getUserWorkout(): Promise<WorkoutTemplateModel[]> {
  try {
    const response = await apiClient.post('/api/Workout/GetUserWorkout', {})

    // Handle response format
    if (response.data?.StatusCode === 200 && response.data?.Result) {
      return response.data.Result
    }

    // Handle direct response
    return response.data || []
  } catch (error) {
    logger.error('Error fetching user workouts:', error)
    throw error
  }
}

/**
 * Get today's workout using the mobile app workflow
 */
export async function getTodaysWorkout(): Promise<WorkoutTemplateGroupModel[]> {
  try {
    // Step 1: Get user's workout program info with timezone
    const programInfo = await getUserWorkoutProgramInfo()

    if (!programInfo?.GetUserProgramInfoResponseModel?.NextWorkoutTemplate) {
      logger.warn('No next workout template found in program info')
      return []
    }

    const nextWorkout =
      programInfo.GetUserProgramInfoResponseModel.NextWorkoutTemplate

    // Step 2: Get detailed workout information
    // eslint-disable-next-line @typescript-eslint/no-use-before-define
    const workoutDetails = await getWorkoutDetails(nextWorkout.Id)

    if (!workoutDetails) {
      logger.warn('Failed to get workout details for ID:', nextWorkout.Id)
      return []
    }

    // Return in the expected format (array of workout template groups)
    return [
      {
        Id: nextWorkout.Id,
        WorkoutTemplates: [workoutDetails],
      },
    ] as WorkoutTemplateGroupModel[]
  } catch (error) {
    logger.error("Error fetching today's workout:", error)
    throw error
  }
}

/**
 * Get full workout details with exercises
 */
export async function getWorkoutDetails(
  workoutId: number
): Promise<WorkoutTemplateModel | null> {
  try {
    // Based on the implementation guide, this should be a POST request
    const response = await apiClient.post(
      '/api/Workout/GetUserCustomizedCurrentWorkout',
      workoutId // Send the workout ID as the body
    )

    logger.debug('GetUserCustomizedCurrentWorkout response:', response.data)
    // Handle both wrapped and direct responses
    if (response.data.StatusCode === 200 && 'Result' in response.data) {
      return response.data.Result
    }

    // If no StatusCode, assume it's a direct response
    return response.data
  } catch (error) {
    logger.error('Error fetching workout details:', error)
    throw error
  }
}

/**
 * Helper to get username from auth state
 */
function getUsernameFromAuth(): string | null {
  return getCurrentUserEmail()
}

/**
 * Add warmups to recommendation if needed
 * This implements the MAUI app behavior where warmups are calculated locally
 * when the API returns WarmupsCount > 0 but empty WarmUpsList
 */
async function addWarmupsIfNeeded(
  recommendation: RecommendationModel,
  request: GetRecommendationForExerciseRequest
): Promise<RecommendationModel> {
  // Check if warmups need to be calculated locally
  if (
    recommendation.WarmupsCount > 0 &&
    (!recommendation.WarmUpsList || recommendation.WarmUpsList.length === 0)
  ) {
    logger.log(
      `🔥 [Warmups] Calculating ${recommendation.WarmupsCount} warmup sets for exercise ${request.ExerciseId}`
    )

    try {
      // TODO: Get user settings for warmup calculation when user preferences are available
      // const userSettings = await getUserSettings()

      // We need exercise name to determine exercise type
      // For now, we'll use a placeholder and enhance this later
      const exerciseName = `Exercise ${request.ExerciseId}` // TODO: Get actual exercise name

      // Get exercise information
      const exerciseInfo = ExerciseHelpers.getExerciseInfo(exerciseName)

      // Create settings object for ExerciseHelpers with default values
      // TODO: Get these from user profile when available
      const exerciseUserSettings = {
        unit: 'kg' as const, // Default to kg
        bodyWeight: 70, // Default 70kg if not set
        barbellWeight: 20, // Default barbell weight
        availablePlates: undefined,
      }

      // Calculate warmups using the new WarmupCalculator
      const warmupConfig = {
        warmupsCount: recommendation.WarmupsCount,
        workingWeight: recommendation.Weight,
        workingReps: recommendation.Reps,
        incrementValue:
          recommendation.Increments?.Kg ||
          ExerciseHelpers.getDefaultIncrement(
            exerciseName,
            exerciseUserSettings.unit === 'kg'
          ),
        minWeight: recommendation.Min?.Kg,
        maxWeight: recommendation.Max?.Kg,
        isPlateAvailable:
          recommendation.isPlateAvailable ||
          ExerciseHelpers.estimatePlateAvailability(exerciseName),
        isBodyweight: recommendation.IsBodyweight || exerciseInfo.isBodyweight,
        barbellWeight: ExerciseHelpers.getBarbellWeight(
          exerciseName,
          exerciseUserSettings
        ),
        availablePlates:
          ExerciseHelpers.getAvailablePlates(exerciseUserSettings),
        userBodyWeight: exerciseUserSettings.bodyWeight || 70, // Default 70kg if not set
        isKg: exerciseUserSettings.unit === 'kg',
      }

      const warmups = WarmupCalculator.computeWarmups(warmupConfig)

      // Add calculated warmups to recommendation
      recommendation.WarmUpsList = warmups

      logger.log(
        `✅ [Warmups] Generated ${warmups.length} warmup sets for exercise ${request.ExerciseId}`
      )
    } catch (error) {
      logger.error(
        `❌ [Warmups] Failed to calculate warmups for exercise ${request.ExerciseId}:`,
        error
      )
      // Don't fail the entire request if warmup calculation fails
    }
  }

  return recommendation
}

/**
 * Get AI recommendation for an exercise
 */
export async function getExerciseRecommendation(
  request: GetRecommendationForExerciseRequest
): Promise<RecommendationModel | null> {
  // Enhanced logging for debugging
  logger.log('🔍 [getExerciseRecommendation] Called with request:', request)

  try {
    // If username not provided, try to get from auth state
    const username = request.Username?.trim() || getUsernameFromAuth()

    if (!username) {
      logger.error('❌ [getExerciseRecommendation] No username available')
      throw new Error('Username is required for exercise recommendations')
    }

    // Clean username (mobile app pattern: remove spaces and lowercase)
    const cleanUsername = username.replace(/\s+/g, '').toLowerCase()

    // Build complete request body following mobile app pattern exactly
    // Note: API expects null, not undefined for missing values
    const requestBody = {
      Username: cleanUsername,
      ExerciseId: request.ExerciseId,
      WorkoutId: request.WorkoutId,
      IsQuickMode:
        request.IsQuickMode !== undefined ? request.IsQuickMode : null,
      LightSessionDays:
        request.LightSessionDays !== undefined &&
        request.LightSessionDays !== null
          ? request.LightSessionDays
          : null,
      SwapedExId: request.SwapedExId || null,
      IsStrengthPhashe:
        request.IsStrengthPhashe !== undefined
          ? request.IsStrengthPhashe
          : false, // Note: API has typo
      IsFreePlan: request.IsFreePlan !== undefined ? request.IsFreePlan : false,
      IsFirstWorkoutOfStrengthPhase:
        request.IsFirstWorkoutOfStrengthPhase !== undefined
          ? request.IsFirstWorkoutOfStrengthPhase
          : false,
      VersionNo: request.VersionNo !== undefined ? request.VersionNo : 1,
      SetStyle: request.SetStyle || 'Normal', // Default to Normal if not specified
      IsFlexibility:
        request.IsFlexibility !== undefined ? request.IsFlexibility : false,
    }

    // Determine endpoint based on SetStyle and exercise type (following mobile app pattern)
    const setStyle = request.SetStyle?.toLowerCase() || 'normal'
    const isRestPause = setStyle === 'restpause' || setStyle === 'rest-pause'

    // Force normal endpoint for flexibility exercises (mobile app logic)
    const shouldUseNormal =
      request.IsFlexibility ||
      request.ExerciseId === 16508 || // specific exercise ID
      !isRestPause

    const endpoint = shouldUseNormal
      ? '/api/Exercise/GetRecommendationNormalRIRForExerciseWithoutWarmupsNew'
      : '/api/Exercise/GetRecommendationRestPauseRIRForExerciseWithoutWarmupsNew'

    logger.log(`🚀 [API] Making request to ${endpoint}`)
    logger.log(`📤 [API] Request body:`, JSON.stringify(requestBody, null, 2))
    logger.log(
      `🎯 [API] SetStyle: ${request.SetStyle}, IsFlexibility: ${request.IsFlexibility} -> Using ${shouldUseNormal ? 'Normal' : 'RestPause'} endpoint`
    )

    const response = await apiClient.post(endpoint, requestBody)

    logger.log(`📥 [API] Response status:`, response.status)
    logger.log(
      `📥 [API] Response data:`,
      JSON.stringify(response.data, null, 2)
    )

    // Check if response is null
    if (
      response.data === null ||
      (response.data?.Result === null && response.data?.StatusCode === 200)
    ) {
      logger.log(
        `⚠️ [API] Null recommendation received for exercise ${request.ExerciseId} - no exercise history`
      )
      return null
    }

    logger.log(
      `🏋️ [API] Weight in response:`,
      response.data?.Weight || response.data?.Result?.Weight || 'NOT FOUND'
    )

    logger.debug(`${endpoint} response:`, response.data)

    // Handle both wrapped and direct responses
    let recommendation: RecommendationModel | null = null
    if (response.data.StatusCode === 200 && 'Result' in response.data) {
      logger.log('✅ [API] Returning wrapped response Result')
      recommendation = response.data.Result
    } else {
      // If no StatusCode, assume it's a direct response
      logger.log('✅ [API] Returning direct response')
      recommendation = response.data
    }

    // Add warmup calculation if needed
    if (recommendation) {
      recommendation = await addWarmupsIfNeeded(recommendation, request)
    }

    return recommendation
  } catch (error) {
    logger.error('❌ [getExerciseRecommendation] Error:', error)
    logger.error('Error fetching exercise recommendation:', error)
    // Return null instead of throwing to allow graceful degradation
    return null
  }
}

/**
 * Generate cache key for a recommendation
 * This will be used by the cache service
 */
export function generateRecommendationCacheKey(
  userId: string,
  exerciseId: number,
  workoutId: number
): string {
  return `${userId}-${exerciseId}-${workoutId}`
}
