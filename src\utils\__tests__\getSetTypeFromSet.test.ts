import { describe, it, expect } from 'vitest'
import { getSetTypeFromSet } from '../getSetTypeFromSet'
import type { WorkoutLogSerieModel } from '@/types'
import type { WorkoutLogSerieModelRef } from '@/types/api/WorkoutLogSerieModelRef'

describe('getSetTypeFromSet', () => {
  const baseSet: WorkoutLogSerieModel & Partial<WorkoutLogSerieModelRef> = {
    Id: 1,
    SetNo: '1',
    Reps: 10,
    Weight: { Lb: 100, Kg: 45 },
    IsFinished: false,
    IsNext: false,
    IsWarmups: false,
  }

  describe('warmup set detection', () => {
    it('should return "Warm Up" for sets with IsWarmups: true', () => {
      const warmupSet = {
        ...baseSet,
        IsWarmups: true,
      }
      expect(getSetTypeFromSet(warmupSet)).toBe('Warm Up')
    })

    it('should prioritize warmup over other set types', () => {
      const warmupWithOtherTypes = {
        ...baseSet,
        IsWarmups: true,
        SetTitle: 'Rest-pause set',
        IsBackOffSet: true,
        IsDropSet: true,
        NbPause: 1,
      }
      expect(getSetTypeFromSet(warmupWithOtherTypes)).toBe('Warm Up')
    })
  })

  describe('SetTitle-based detection', () => {
    it('should detect rest-pause from SetTitle', () => {
      const restPauseSet = {
        ...baseSet,
        SetTitle: 'Rest-pause set 1',
      }
      expect(getSetTypeFromSet(restPauseSet)).toBe('Rest-pause')
    })

    it('should detect pyramid from SetTitle', () => {
      const pyramidSet = {
        ...baseSet,
        SetTitle: 'Pyramid set:',
      }
      expect(getSetTypeFromSet(pyramidSet)).toBe('Pyramid')
    })

    it('should detect reverse pyramid from SetTitle', () => {
      const reversePyramidSet = {
        ...baseSet,
        SetTitle: 'Reverse pyramid set',
      }
      expect(getSetTypeFromSet(reversePyramidSet)).toBe('Reverse pyramid')
    })

    it('should detect back-off from SetTitle', () => {
      const backOffSet = {
        ...baseSet,
        SetTitle: 'Back-off set:',
      }
      expect(getSetTypeFromSet(backOffSet)).toBe('Back-off')
    })

    it('should detect drop set from SetTitle', () => {
      const dropSet = {
        ...baseSet,
        SetTitle: 'Drop set',
      }
      expect(getSetTypeFromSet(dropSet)).toBe('Drop set')
    })
  })

  describe('property-based detection', () => {
    it('should detect back-off from IsBackOffSet property', () => {
      const backOffSet = {
        ...baseSet,
        IsBackOffSet: true,
      }
      expect(getSetTypeFromSet(backOffSet)).toBe('Back-off')
    })

    it('should detect drop set from IsDropSet property', () => {
      const dropSet = {
        ...baseSet,
        IsDropSet: true,
      }
      expect(getSetTypeFromSet(dropSet)).toBe('Drop set')
    })

    it('should detect rest-pause from NbPause property', () => {
      const restPauseSet = {
        ...baseSet,
        NbPause: 1,
      }
      expect(getSetTypeFromSet(restPauseSet)).toBe('Rest-pause')
    })
  })

  describe('normal sets', () => {
    it('should return "Normal" for sets without special properties', () => {
      expect(getSetTypeFromSet(baseSet)).toBe('Normal')
    })

    it('should return "Normal" for work sets without set type indicators', () => {
      const normalWorkSet = {
        ...baseSet,
        SetTitle: 'Working sets:',
      }
      expect(getSetTypeFromSet(normalWorkSet)).toBe('Normal')
    })
  })

  describe('case insensitive detection', () => {
    it('should handle uppercase SetTitle', () => {
      const upperCaseSet = {
        ...baseSet,
        SetTitle: 'REST-PAUSE SET',
      }
      expect(getSetTypeFromSet(upperCaseSet)).toBe('Rest-pause')
    })

    it('should handle mixed case SetTitle', () => {
      const mixedCaseSet = {
        ...baseSet,
        SetTitle: 'BaCk-OfF sEt',
      }
      expect(getSetTypeFromSet(mixedCaseSet)).toBe('Back-off')
    })
  })
})
