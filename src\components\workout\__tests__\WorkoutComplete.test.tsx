import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { vi } from 'vitest'
import { WorkoutComplete } from '../WorkoutComplete'
import { useUserStatsStore } from '@/stores/userStatsStore'
import { useRouter } from 'next/navigation'
import { useWorkout } from '@/hooks/useWorkout'

// Mock the hooks
vi.mock('next/navigation', () => ({
  useRouter: vi.fn(() => ({
    push: vi.fn(),
  })),
}))

vi.mock('@/hooks/useWorkout', () => ({
  useWorkout: vi.fn(() => ({
    workoutSession: {
      workoutId: '123',
      startTime: '2024-01-01T10:00:00Z',
      endTime: '2024-01-01T11:00:00Z',
      exercises: [
        {
          id: '1',
          name: 'Bench Press',
          sets: [
            {
              id: '1',
              reps: 10,
              weight: { Lb: 135, Kg: 61 },
              isWarmup: false,
              rir: 2,
            },
            {
              id: '2',
              reps: 8,
              weight: { Lb: 155, Kg: 70 },
              isWarmup: false,
              rir: 1,
            },
          ],
        },
      ],
    },
    finishWorkout: vi.fn(),
    isLoading: false,
    error: null,
  })),
}))

vi.mock('@/stores/userStatsStore', () => ({
  useUserStatsStore: vi.fn(() => ({
    stats: {
      weekStreak: 5,
      workoutsCompleted: 42,
      lbsLifted: 125000,
    },
    isLoading: false,
    error: null,
    fetchStats: vi.fn(),
  })),
}))

describe('WorkoutComplete', () => {
  describe('Theme Application', () => {
    it('should use theme-aware background instead of hardcoded gray-50', () => {
      const { container } = render(<WorkoutComplete />)

      const mainDiv = container.querySelector('.flex.flex-col.h-full')
      expect(mainDiv).toHaveClass('bg-bg-primary')
      expect(mainDiv).not.toHaveClass('bg-gray-50')
    })

    it('should use theme-aware text colors instead of hardcoded gray-900', () => {
      render(<WorkoutComplete />)

      // Check that stats labels use theme-aware colors
      const exercisesLabel = screen.getByText('Exercises')
      expect(exercisesLabel).toHaveClass('text-text-secondary')
      expect(exercisesLabel).not.toHaveClass('text-gray-600')
    })

    it('should use theme-aware card background instead of hardcoded white', () => {
      const { container } = render(<WorkoutComplete />)

      const summaryCard = container.querySelector(
        '.rounded-theme.shadow-theme-md'
      )
      expect(summaryCard).toHaveClass('bg-bg-secondary')
      expect(summaryCard).not.toHaveClass('bg-white')
    })

    it('should use theme-aware text colors for labels instead of hardcoded gray-600', () => {
      render(<WorkoutComplete />)

      const exercisesLabel = screen.getByText('Exercises')
      expect(exercisesLabel).toHaveClass('text-text-secondary')
      expect(exercisesLabel).not.toHaveClass('text-gray-600')
    })

    // Skip testing personal records section as it relies on multiple useWorkout calls
    // and the mock doesn't properly support this pattern

    // Skip testing offline section as it relies on multiple useWorkout calls
    // and the mock doesn't properly support this pattern

    it('should use theme-aware button colors instead of hardcoded blue-600', () => {
      render(<WorkoutComplete />)

      const finishButton = screen.getByRole('button', {
        name: /return to program page/i,
      })
      expect(finishButton).toHaveClass('from-brand-primary')
      expect(finishButton).toHaveClass('to-brand-secondary')
      expect(finishButton).toHaveClass('text-text-inverse')
      expect(finishButton).not.toHaveClass('bg-blue-600')
      expect(finishButton).not.toHaveClass('hover:bg-blue-700')
    })

    it('should use theme-aware floating button container', () => {
      render(<WorkoutComplete />)

      const floatingContainer = screen.getByTestId('floating-cta-container')
      expect(floatingContainer).toHaveClass('fixed')
      expect(floatingContainer).toHaveClass('bottom-6')
      expect(floatingContainer).not.toHaveClass('bg-white')
      expect(floatingContainer).not.toHaveClass('border-gray-200')
    })
  })

  describe('Original Functionality', () => {
    it('should render workout summary', () => {
      render(<WorkoutComplete />)

      // Check stats are displayed without heading
      expect(screen.getByText('1')).toBeInTheDocument() // exercises count
      expect(screen.getByText('2')).toBeInTheDocument() // sets count
    })

    it('should render finish button', () => {
      render(<WorkoutComplete />)

      const button = screen.getByRole('button', {
        name: /Return to program page/i,
      })
      expect(button).toHaveTextContent('Back to Home')
    })
  })

  describe('Workout Streak Display', () => {
    it('should display workout streak in stats box', () => {
      render(<WorkoutComplete />)

      expect(screen.getByText('Workout Streak')).toBeInTheDocument()
      expect(screen.getByText('5 weeks')).toBeInTheDocument()
    })

    it('should show streak icon', () => {
      render(<WorkoutComplete />)

      // Find the workout streak label container which contains the icon
      const streakLabel = screen.getByText('Workout Streak')
      const streakContainer = streakLabel.parentElement
      const streakIcon = streakContainer?.querySelector('svg')

      expect(streakIcon).toBeInTheDocument()
      expect(streakIcon).toHaveAttribute('aria-hidden', 'true')
      expect(streakIcon).toHaveAttribute('width', '18')
    })

    it('should handle singular week correctly', () => {
      vi.mocked(useUserStatsStore).mockReturnValue({
        stats: {
          weekStreak: 1,
          workoutsCompleted: 42,
          lbsLifted: 125000,
        },
        isLoading: false,
        error: null,
        fetchStats: vi.fn(),
      } as any)

      render(<WorkoutComplete />)

      expect(screen.getByText('1 week')).toBeInTheDocument()
    })

    it('should handle loading state for user stats', () => {
      vi.mocked(useUserStatsStore).mockReturnValue({
        stats: null,
        isLoading: true,
        error: null,
        fetchStats: vi.fn(),
      } as any)

      render(<WorkoutComplete />)

      expect(screen.getByText('Workout Streak')).toBeInTheDocument()
      expect(screen.getByText('Loading...')).toBeInTheDocument()
    })
  })

  describe('Workout Data Persistence and Reset', () => {
    it('should display workout data on summary page', () => {
      // Test rationale: Verify workout data is preserved for summary display
      render(<WorkoutComplete />)

      // Verify workout data is shown
      expect(screen.getByText('1')).toBeInTheDocument() // exercises
      expect(screen.getByText('2')).toBeInTheDocument() // sets
      expect(screen.queryByText('No workout data')).not.toBeInTheDocument()
    })

    it('should show "No workout data" when session is null', () => {
      // Test rationale: Verify appropriate message when no data available
      vi.mocked(useWorkout).mockReturnValue({
        workoutSession: null,
        finishWorkout: vi.fn(),
        isLoading: false,
        error: null,
      } as any)

      render(<WorkoutComplete />)

      expect(screen.getByText('No workout data')).toBeInTheDocument()
    })

    it('should call resetWorkout when clicking "Back to Home"', async () => {
      // Test rationale: resetWorkout should be called before navigating away
      // This ensures clean state when returning to workout page
      const mockPush = vi.fn()
      const mockFinishWorkout = vi.fn()
      const mockResetWorkout = vi.fn()

      vi.mocked(useRouter).mockReturnValue({
        push: mockPush,
      } as any)

      vi.mocked(useWorkout).mockReturnValue({
        workoutSession: {
          workoutId: '123',
          startTime: '2024-01-01T10:00:00Z',
          endTime: '2024-01-01T11:00:00Z',
          exercises: [
            {
              id: '1',
              name: 'Bench Press',
              sets: [
                {
                  id: '1',
                  reps: 10,
                  weight: { Lb: 135, Kg: 61 },
                  isWarmup: false,
                  rir: 2,
                },
              ],
            },
          ],
        },
        finishWorkout: mockFinishWorkout,
        resetWorkout: mockResetWorkout,
        isLoading: false,
        error: null,
      } as any)

      const user = userEvent.setup()
      render(<WorkoutComplete />)

      const button = screen.getByRole('button', {
        name: /Return to program page/i,
      })

      await user.click(button)

      // Verify navigation happens
      expect(mockFinishWorkout).toHaveBeenCalled()
      expect(mockPush).toHaveBeenCalledWith('/program')

      // Verify resetWorkout IS called before navigation
      expect(mockResetWorkout).toHaveBeenCalled()
    })
  })
})
