'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useWorkout } from '@/hooks/useWorkout'
import { FloatingCTAButton } from '@/components/ui'
import { useUserStatsStore } from '@/stores/userStatsStore'
import { ShareButton } from '@/components/navigation/ShareButton'
import {
  WorkoutStatsDisplay,
  calculateWorkoutStats,
  formatVolume,
} from './WorkoutStatsDisplay'

export function WorkoutComplete() {
  const router = useRouter()
  const { workoutSession, finishWorkout, resetWorkout, isLoading, error } =
    useWorkout()
  const [saveError, setSaveError] = useState<string | null>(null)

  // Fetch user stats for workout streak
  const {
    stats: userStats,
    isLoading: isLoadingStats,
    fetchStats,
  } = useUserStatsStore()

  useEffect(() => {
    fetchStats()
  }, [fetchStats])

  const stats = calculateWorkoutStats(workoutSession)

  const handleFinishWorkout = async () => {
    try {
      setSaveError(null)
      await finishWorkout()
      // Reset workout state before navigating away from summary
      resetWorkout()
      router.push('/program')
    } catch (err) {
      setSaveError('Failed to save workout')
    }
  }

  const getButtonLabel = () => {
    if (error) return 'Retry'
    if (isLoading) return 'Saving...'
    return 'Back to Home'
  }

  const getButtonAriaLabel = () => {
    if (error) return 'Retry saving workout'
    if (isLoading) return 'Saving workout data'
    return 'Return to program page'
  }

  // Check for personal records (mock data for now)
  const personalRecords =
    (
      useWorkout() as unknown as {
        personalRecords?: Array<{
          exercise: string
          type: string
          value: string
        }>
      }
    ).personalRecords || []

  // Check offline status
  const isOffline =
    (useWorkout() as unknown as { isOffline?: boolean }).isOffline || false

  if (!workoutSession) {
    return (
      <div className="flex flex-col items-center justify-center h-full p-6">
        <p className="text-text-secondary">No workout data</p>
      </div>
    )
  }

  return (
    <div className="flex flex-col h-full bg-bg-primary">
      {/* Celebration Animation */}
      <div
        data-testid="celebration-animation"
        className="absolute inset-0 pointer-events-none overflow-hidden"
      >
        {/* Add confetti or animation here */}
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-y-auto p-6 pb-24">
        {/* Stats Container */}
        <div data-testid="workout-stats" className="space-y-6">
          {/* Summary Stats */}
          <WorkoutStatsDisplay
            stats={stats}
            userStats={userStats}
            isLoadingStats={isLoadingStats}
          />

          {/* Share Button */}
          <div className="flex justify-center">
            <ShareButton
              shareData={{
                title: 'Workout Complete!',
                text: `I just completed my workout with Dr. Muscle X! ${stats?.totalExercises || 0} exercises, ${stats?.totalSets || 0} sets, ${stats ? formatVolume(stats.totalVolume) : '0 lbs'} lifted. ${userStats?.weekStreak || 0} week${userStats?.weekStreak === 1 ? '' : 's'} streak! 💪 https://dr-muscle.com`,
              }}
              className="flex items-center gap-3 px-6 py-3 min-h-[44px] bg-bg-secondary hover:bg-bg-tertiary text-text-primary font-medium rounded-theme shadow-theme-sm hover:shadow-theme-md transition-all duration-200 border border-brand-primary/10 hover:border-brand-primary/20"
              showLabel
            />
          </div>

          {/* Personal Records */}
          {personalRecords.length > 0 && (
            <div className="bg-success/10 border border-success/20 rounded-lg p-4">
              <p className="text-success font-semibold mb-2">
                New Personal Record!
              </p>
              {personalRecords.map((pr) => (
                <p
                  key={`${pr.exercise}-${pr.type}`}
                  className="text-sm text-success/80"
                >
                  {pr.exercise} - {pr.type}: {pr.value}
                </p>
              ))}
            </div>
          )}

          {/* Offline Mode */}
          {isOffline && (
            <div className="bg-info/10 border border-info/20 rounded-lg p-4">
              <p className="text-info">Offline mode</p>
              <p className="text-sm text-info/80 mt-1">
                Your workout will sync when connected
              </p>
            </div>
          )}

          {/* Error Display */}
          {(error || saveError) && (
            <div className="bg-error/10 border border-error/20 rounded-lg p-4">
              <p className="text-error">
                {error || saveError || 'An error occurred'}
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Floating CTA Button */}
      <FloatingCTAButton
        onClick={handleFinishWorkout}
        label={getButtonLabel()}
        ariaLabel={getButtonAriaLabel()}
      />
    </div>
  )
}
